# Cloudflare R2 存储设置指南

## 1. 创建 R2 存储桶

使用 Wrangler CLI 创建 R2 存储桶：

```bash
# 创建存储桶
wrangler r2 bucket create hono-cloudflare-workers-storage

# 查看存储桶列表
wrangler r2 bucket list
```

## 2. 配置 CORS（跨域资源共享）

如果需要从浏览器直接上传文件，需要配置 CORS：

```bash
# 创建 CORS 配置文件
cat > cors-config.json << EOF
[
  {
    "AllowedOrigins": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST", "DELETE"],
    "AllowedHeaders": ["*"],
    "ExposeHeaders": ["ETag"],
    "MaxAgeSeconds": 3000
  }
]
EOF

# 应用 CORS 配置
wrangler r2 bucket cors put hono-cloudflare-workers-storage --file cors-config.json

# 查看 CORS 配置
wrangler r2 bucket cors get hono-cloudflare-workers-storage
```

## 3. 设置生命周期规则（可选）

为了自动清理临时文件，可以设置生命周期规则：

```bash
# 创建生命周期配置文件
cat > lifecycle-config.json << EOF
{
  "Rules": [
    {
      "ID": "DeleteTempFiles",
      "Status": "Enabled",
      "Filter": {
        "Prefix": "temp/"
      },
      "Expiration": {
        "Days": 7
      }
    },
    {
      "ID": "DeleteIncompleteMultipartUploads",
      "Status": "Enabled",
      "AbortIncompleteMultipartUpload": {
        "DaysAfterInitiation": 1
      }
    }
  ]
}
EOF

# 应用生命周期配置
wrangler r2 bucket lifecycle put hono-cloudflare-workers-storage --file lifecycle-config.json

# 查看生命周期配置
wrangler r2 bucket lifecycle get hono-cloudflare-workers-storage
```

## 4. 配置自定义域名（可选）

如果需要使用自定义域名访问文件：

1. 在 Cloudflare 仪表板中添加自定义域名
2. 创建 CNAME 记录指向 R2 存储桶
3. 配置 SSL/TLS 设置

## 5. 本地开发配置

对于本地开发，Wrangler 会自动处理 R2 存储桶的模拟。确保在 `wrangler.jsonc` 中正确配置了 R2 绑定。

## 6. 环境变量

确保在生产环境中设置以下环境变量：

```bash
# 在 Cloudflare Workers 仪表板中设置
R2_BUCKET_NAME=hono-cloudflare-workers-storage
R2_PUBLIC_URL=https://your-domain.com
```

## 7. 权限设置

确保 Worker 有访问 R2 存储桶的权限：

1. 在 Cloudflare 仪表板中检查 Worker 的权限
2. 确保 R2 存储桶绑定正确配置
3. 验证 API Token 有足够的权限

## 8. 监控和日志

启用 R2 存储桶的监控和日志记录：

```bash
# 查看存储桶使用情况
wrangler r2 bucket info hono-cloudflare-workers-storage

# 查看存储桶中的对象
wrangler r2 object list hono-cloudflare-workers-storage --limit 10
```

## 9. 安全最佳实践

1. **访问控制**：确保只有授权用户可以上传/访问文件
2. **文件验证**：验证文件类型和大小
3. **签名 URL**：使用带过期时间的签名 URL
4. **内容扫描**：对上传的文件进行恶意软件扫描
5. **备份策略**：定期备份重要文件

## 10. 故障排除

常见问题和解决方案：

- **403 错误**：检查权限和 CORS 配置
- **404 错误**：验证存储桶名称和对象键
- **上传失败**：检查文件大小限制和网络连接
- **访问慢**：考虑使用 CDN 或优化文件大小
