{"dependencies": {"@hono/zod-openapi": "^1.1.0", "@hono/zod-validator": "^0.7.2", "better-auth": "^1.3.6", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "hono": "^4.9.2", "zod": "^4.1.1"}, "devDependencies": {"@biomejs/biome": "^2.2.2", "@cloudflare/vite-plugin": "^1.11.7", "@types/bun": "^1.2.20", "drizzle-kit": "^0.31.4", "vite": "^7.1.3", "vite-ssr-components": "^0.5.0", "wrangler": "^4.32.0"}, "name": "hono-cloudflare-workers", "scripts": {"build": "vite build", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "deploy": "$npm_execpath run build && wrangler deploy", "dev": "vite --host", "preview": "$npm_execpath run build && vite preview"}, "type": "module"}