{"$schema": "https://biomejs.dev/schemas/2.2.0/schema.json", "assist": {"actions": {"source": {"organizeImports": "on", "recommended": true, "useSortedKeys": "on", "useSortedProperties": "on"}}, "enabled": true}, "files": {"ignoreUnknown": false, "includes": ["**", "!**/worker-configuration.d.ts"]}, "formatter": {"enabled": true, "indentStyle": "space"}, "javascript": {"formatter": {"jsxQuoteStyle": "single", "quoteStyle": "single", "semicolons": "asNeeded"}}, "linter": {"enabled": true, "rules": {"recommended": true}}, "vcs": {"clientKind": "git", "enabled": false, "useIgnoreFile": false}}