// 文件相关的类型定义

export interface FileUploadRequest {
  /** 文件分类 */
  category?: string
  /** 自定义元数据 */
  metadata?: Record<string, string>
}

export interface FileInfo {
  /** 文件键名（路径） */
  key: string
  /** 文件大小（字节） */
  size: number
  /** 文件 ETag */
  etag: string
  /** 上传时间 */
  uploaded: Date
  /** 文件的 MIME 类型 */
  contentType?: string
  /** 自定义元数据 */
  metadata?: Record<string, string>
  /** 访问 URL */
  accessUrl?: string
}

export interface FileListResponse {
  /** 文件列表 */
  files: FileInfo[]
  /** 是否还有更多文件 */
  truncated: boolean
  /** 分页游标 */
  cursor?: string
}

export interface FileUploadResponse {
  success: boolean
  data: FileInfo
}

export interface FileListRequest {
  /** 文件分类过滤 */
  category?: string
  /** 每页数量 */
  limit?: number
  /** 分页游标 */
  cursor?: string
}

export interface FileDeleteResponse {
  success: boolean
  message: string
}

// 支持的文件类型配置
export const SUPPORTED_FILE_TYPES = {
  images: [
    'image/jpeg',
    'image/png', 
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ],
  documents: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  ],
  text: [
    'text/plain',
    'text/csv',
    'application/json',
    'application/xml'
  ],
  archives: [
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed'
  ],
  audio: [
    'audio/mpeg',
    'audio/wav',
    'audio/ogg'
  ],
  video: [
    'video/mp4',
    'video/x-msvideo',
    'video/quicktime',
    'video/webm'
  ]
} as const

// 文件大小限制（字节）
export const FILE_SIZE_LIMITS = {
  /** 默认限制：10MB */
  default: 10 * 1024 * 1024,
  /** 图片限制：5MB */
  image: 5 * 1024 * 1024,
  /** 文档限制：20MB */
  document: 20 * 1024 * 1024,
  /** 视频限制：100MB */
  video: 100 * 1024 * 1024,
  /** 音频限制：50MB */
  audio: 50 * 1024 * 1024,
} as const

// 文件分类
export type FileCategory = 
  | 'avatar'      // 头像
  | 'attachment'  // 附件
  | 'image'       // 图片
  | 'document'    // 文档
  | 'general'     // 通用
  | 'temp'        // 临时文件

// 错误类型
export interface FileError {
  code: string
  message: string
}

export const FILE_ERRORS = {
  FILE_NOT_FOUND: { code: 'FILE_NOT_FOUND', message: '文件不存在' },
  FILE_TOO_LARGE: { code: 'FILE_TOO_LARGE', message: '文件大小超出限制' },
  INVALID_FILE_TYPE: { code: 'INVALID_FILE_TYPE', message: '不支持的文件类型' },
  UPLOAD_FAILED: { code: 'UPLOAD_FAILED', message: '文件上传失败' },
  DELETE_FAILED: { code: 'DELETE_FAILED', message: '文件删除失败' },
  ACCESS_DENIED: { code: 'ACCESS_DENIED', message: '访问被拒绝' },
  EXPIRED_LINK: { code: 'EXPIRED_LINK', message: '链接已过期' },
} as const
