// Todo 相关的类型定义

export type Priority = 'low' | 'medium' | 'high'

export interface Todo {
  id: string
  title: string
  description?: string
  completed: boolean
  priority: Priority
  dueDate?: Date
  completedAt?: Date
  userId: string
  categoryId?: string
  parentId?: string
  sortOrder: number
  createdAt: Date
  updatedAt: Date
}

export interface Category {
  id: string
  name: string
  color: string
  icon?: string
  userId: string
  createdAt: Date
  updatedAt: Date
}

export interface Tag {
  id: string
  name: string
  color: string
  userId: string
  createdAt: Date
}

export interface TodoTag {
  id: string
  todoId: string
  tagId: string
  createdAt: Date
}

export interface TodoAttachment {
  id: string
  todoId: string
  fileName: string
  fileUrl: string
  fileSize?: number
  mimeType?: string
  createdAt: Date
}

export interface TodoComment {
  id: string
  todoId: string
  userId: string
  content: string
  createdAt: Date
  updatedAt: Date
}

// API 请求/响应类型
export interface CreateTodoRequest {
  title: string
  description?: string
  priority?: Priority
  dueDate?: string
  categoryId?: string
  parentId?: string
}

export interface UpdateTodoRequest {
  title?: string
  description?: string
  completed?: boolean
  priority?: Priority
  dueDate?: string
  categoryId?: string
}

export interface CreateCategoryRequest {
  name: string
  color?: string
  icon?: string
}

export interface CreateTagRequest {
  name: string
  color?: string
}

export interface TodoWithCategory extends Todo {
  category?: {
    id: string
    name: string
    color: string
    icon?: string
  }
}

export interface TodoWithTags extends Todo {
  tags: Tag[]
}

export interface TodoWithAll extends Todo {
  category?: Category
  tags: Tag[]
  comments: TodoComment[]
  attachments: TodoAttachment[]
  subtasks: Todo[]
}
