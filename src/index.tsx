import { Hono } from 'hono'
import { requestId } from 'hono/request-id'
import z from 'zod'
import { authMiddlewareHandler, corsMiddlewareHandler } from '@/middleware'
import { renderer } from '@/renderer'
import { authRoutes } from '@/routes'
import todosRouter from '@/routes/todos'
import { formatValidationError } from './lib/validation-formatter'

const app = new Hono<HonoContext>()

// 中间件设置
app.use(renderer)
app.use('*', requestId())
app.use('*', corsMiddlewareHandler)
app.use('*', authMiddlewareHandler)

// app.route('/api', postRoutes)
app.route('/api', todosRouter)
app.route('/api', authRoutes)
app.get('/', (c) => {
  return c.render(<h1>Hello!</h1>)
})
// 在中间件中使用
app.onError((err, c) => {
  console.error(err)
  if (err instanceof z.ZodError) {
    const formattedError = formatValidationError(err)
    return c.json(formattedError, 400)
  }
  // ... 其他错误处理
  return c.json(
    {
      message: err.message,
    },
    500,
  )
})

export default app
