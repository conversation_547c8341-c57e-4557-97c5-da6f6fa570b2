import { Hono } from 'hono'
import { authMiddlewareHandler, corsMiddlewareHandler } from '@/middleware'
import { renderer } from '@/renderer'
import { authRoutes, postRoutes } from '@/routes'
import customers from '@/routes/customer'
import todosRouter from '@/routes/todos'

const app = new Hono<HonoContext>()

// 中间件设置
app.use(renderer)
app.use('*', corsMiddlewareHandler)
app.use('*', authMiddlewareHandler)

app.get('/', (c) => {
  return c.render(<h1>Hello!</h1>)
})
app.route('/api', postRoutes)
app.route('/api', customers)
app.route('/api', todosRouter)
app.route('/api', authRoutes)

export default app
