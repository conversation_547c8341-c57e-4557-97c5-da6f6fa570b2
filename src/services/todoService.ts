import { and, asc, count, desc, eq, isNull, sql } from 'drizzle-orm'
import { drizzle } from 'drizzle-orm/d1'
import { categories, todos } from '@/db/schema/todos'
import type {
  CreateCategoryRequest,
  CreateTodoRequest,
  Priority,
  UpdateTodoRequest,
} from '@/types/todos'

export class TodoService {
  private db: ReturnType<typeof drizzle>

  constructor(database: D1Database) {
    this.db = drizzle(database)
  }

  // Todo 相关方法
  async getTodosByUser(
    userId: string,
    filters?: {
      completed?: boolean
      categoryId?: string
      priority?: Priority
      parentId?: string | null
    },
  ) {
    let query = this.db
      .select({
        category: {
          color: categories.color,
          icon: categories.icon,
          id: categories.id,
          name: categories.name,
        },
        completed: todos.completed,
        completedAt: todos.completedAt,
        createdAt: todos.createdAt,
        description: todos.description,
        dueDate: todos.dueDate,
        id: todos.id,
        priority: todos.priority,
        sortOrder: todos.sortOrder,
        title: todos.title,
        updatedAt: todos.updatedAt,
      })
      .from(todos)
      .leftJoin(categories, eq(todos.categoryId, categories.id))
      .where(eq(todos.userId, userId))

    // 应用过滤器
    if (filters?.completed !== undefined) {
      query = query.where(
        and(eq(todos.userId, userId), eq(todos.completed, filters.completed)),
      )
    }
    if (filters?.categoryId) {
      query = query.where(
        and(eq(todos.userId, userId), eq(todos.categoryId, filters.categoryId)),
      )
    }
    if (filters?.priority) {
      query = query.where(
        and(eq(todos.userId, userId), eq(todos.priority, filters.priority)),
      )
    }
    if (filters?.parentId === null) {
      query = query.where(and(eq(todos.userId, userId), isNull(todos.parentId)))
    } else if (filters?.parentId) {
      query = query.where(
        and(eq(todos.userId, userId), eq(todos.parentId, filters.parentId)),
      )
    }

    return await query.orderBy(asc(todos.sortOrder), desc(todos.createdAt))
  }

  async getTodoById(todoId: string, userId: string) {
    const result = await this.db
      .select()
      .from(todos)
      .where(and(eq(todos.id, todoId), eq(todos.userId, userId)))
      .limit(1)

    return result[0] || null
  }

  async createTodo(userId: string, data: CreateTodoRequest) {
    const result = await this.db
      .insert(todos)
      .values({
        categoryId: data.categoryId,
        description: data.description,
        dueDate: data.dueDate ? new Date(data.dueDate) : null,
        parentId: data.parentId,
        priority: data.priority || 'medium',
        title: data.title,
        userId,
      })
      .returning()

    return result[0]
  }

  async updateTodo(todoId: string, userId: string, data: UpdateTodoRequest) {
    const updateData: any = {
      updatedAt: new Date(),
    }

    if (data.title !== undefined) updateData.title = data.title
    if (data.description !== undefined)
      updateData.description = data.description
    if (data.completed !== undefined) {
      updateData.completed = data.completed
      updateData.completedAt = data.completed ? new Date() : null
    }
    if (data.priority !== undefined) updateData.priority = data.priority
    if (data.dueDate !== undefined)
      updateData.dueDate = data.dueDate ? new Date(data.dueDate) : null
    if (data.categoryId !== undefined) updateData.categoryId = data.categoryId

    const result = await this.db
      .update(todos)
      .set(updateData)
      .where(and(eq(todos.id, todoId), eq(todos.userId, userId)))
      .returning()

    return result[0] || null
  }

  async deleteTodo(todoId: string, userId: string) {
    const result = await this.db
      .delete(todos)
      .where(and(eq(todos.id, todoId), eq(todos.userId, userId)))
      .returning()

    return result[0] || null
  }

  async getSubtasks(parentId: string, userId: string) {
    return await this.db
      .select()
      .from(todos)
      .where(and(eq(todos.userId, userId)))
      .orderBy(asc(todos.sortOrder), desc(todos.createdAt))
  }

  // Category 相关方法
  async getCategoriesByUser(userId: string) {
    return await this.db
      .select()
      .from(categories)
      .where(eq(categories.userId, userId))
      .orderBy(asc(categories.name))
  }

  async createCategory(userId: string, data: CreateCategoryRequest) {
    const result = await this.db
      .insert(categories)
      .values({
        color: data.color || '#3B82F6',
        icon: data.icon,
        name: data.name,
        userId,
      })
      .returning()

    return result[0]
  }

  async deleteCategory(categoryId: string, userId: string) {
    const result = await this.db
      .delete(categories)
      .where(and(eq(categories.id, categoryId), eq(categories.userId, userId)))
      .returning()

    return result[0] || null
  }

  // 统计相关方法
  async getTodoStats(userId: string) {
    const totalTodos = await this.db
      .select({ count: count() })
      .from(todos)
      .where(eq(todos.userId, userId))

    const completedTodos = await this.db
      .select({ count: count() })
      .from(todos)
      .where(and(eq(todos.userId, userId), eq(todos.completed, true)))

    const pendingTodos = await this.db
      .select({ count: count() })
      .from(todos)
      .where(and(eq(todos.userId, userId), eq(todos.completed, false)))

    const overdueTodos = await this.db
      .select({ count: count() })
      .from(todos)
      .where(
        and(
          eq(todos.userId, userId),
          eq(todos.completed, false),
          sql`${todos.dueDate} < ${new Date()}`,
        ),
      )

    return {
      completed: completedTodos[0].count,
      overdue: overdueTodos[0].count,
      pending: pendingTodos[0].count,
      total: totalTodos[0].count,
    }
  }
}
