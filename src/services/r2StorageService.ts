import type { R2Bucket } from '@cloudflare/workers-types'

export interface UploadOptions {
  /** 文件的 MIME 类型 */
  contentType?: string
  /** 自定义元数据 */
  metadata?: Record<string, string>
  /** 缓存控制 */
  cacheControl?: string
  /** 内容处置 */
  contentDisposition?: string
}

export interface FileInfo {
  /** 文件键名 */
  key: string
  /** 文件大小（字节） */
  size: number
  /** 文件 ETag */
  etag: string
  /** 上传时间 */
  uploaded: Date
  /** 文件的 MIME 类型 */
  contentType?: string
  /** 自定义元数据 */
  metadata?: Record<string, string>
}

export class R2StorageService {
  private bucket: R2Bucket

  constructor(bucket: R2Bucket) {
    this.bucket = bucket
  }

  /**
   * 上传文件到 R2
   * @param key 文件键名（路径）
   * @param file 文件内容
   * @param options 上传选项
   */
  async uploadFile(
    key: string,
    file: ArrayBuffer | ArrayBufferView | string | ReadableStream | Blob,
    options?: UploadOptions
  ): Promise<FileInfo> {
    const putOptions: any = {}

    if (options?.contentType || options?.cacheControl || options?.contentDisposition) {
      putOptions.httpMetadata = {
        contentType: options.contentType,
        cacheControl: options.cacheControl,
        contentDisposition: options.contentDisposition,
      }
    }

    if (options?.metadata) {
      putOptions.customMetadata = options.metadata
    }

    const result = await this.bucket.put(key, file, putOptions)
    
    if (!result) {
      throw new Error('文件上传失败')
    }

    return {
      key: result.key,
      size: result.size,
      etag: result.etag,
      uploaded: result.uploaded,
      contentType: result.httpMetadata?.contentType,
      metadata: result.customMetadata,
    }
  }

  /**
   * 获取文件
   * @param key 文件键名
   */
  async getFile(key: string) {
    return await this.bucket.get(key)
  }

  /**
   * 获取文件信息（不下载文件内容）
   * @param key 文件键名
   */
  async getFileInfo(key: string): Promise<FileInfo | null> {
    const result = await this.bucket.head(key)
    
    if (!result) {
      return null
    }

    return {
      key: result.key,
      size: result.size,
      etag: result.etag,
      uploaded: result.uploaded,
      contentType: result.httpMetadata?.contentType,
      metadata: result.customMetadata,
    }
  }

  /**
   * 删除文件
   * @param key 文件键名
   */
  async deleteFile(key: string): Promise<void> {
    await this.bucket.delete(key)
  }

  /**
   * 删除多个文件
   * @param keys 文件键名数组
   */
  async deleteFiles(keys: string[]): Promise<void> {
    await this.bucket.delete(keys)
  }

  /**
   * 列出文件
   * @param prefix 前缀过滤
   * @param limit 限制数量
   * @param cursor 分页游标
   */
  async listFiles(prefix?: string, limit?: number, cursor?: string) {
    return await this.bucket.list({
      prefix,
      limit,
      cursor,
      include: ['httpMetadata', 'customMetadata'],
    })
  }

  /**
   * 生成预签名 URL（用于直接访问文件）
   * 注意：R2 不直接支持预签名 URL，需要通过 Workers 路由来实现
   * @param key 文件键名
   * @param expiresIn 过期时间（秒）
   */
  generateSignedUrl(key: string, expiresIn: number = 3600): string {
    // 这里返回通过 Workers 访问的 URL
    // 实际的签名验证需要在路由中实现
    const expires = Math.floor(Date.now() / 1000) + expiresIn
    const baseUrl = '/api/files'
    return `${baseUrl}/${encodeURIComponent(key)}?expires=${expires}`
  }

  /**
   * 生成用户专用的文件键名
   * @param userId 用户ID
   * @param filename 原始文件名
   * @param category 文件分类（可选）
   */
  static generateUserFileKey(userId: string, filename: string, category?: string): string {
    const timestamp = Date.now()
    const randomSuffix = Math.random().toString(36).substring(2, 8)
    const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_')
    
    const parts = ['users', userId]
    if (category) {
      parts.push(category)
    }
    parts.push(`${timestamp}_${randomSuffix}_${sanitizedFilename}`)
    
    return parts.join('/')
  }

  /**
   * 从文件扩展名推断 MIME 类型
   * @param filename 文件名
   */
  static getMimeType(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop()
    const mimeTypes: Record<string, string> = {
      // 图片
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      svg: 'image/svg+xml',
      // 文档
      pdf: 'application/pdf',
      doc: 'application/msword',
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xls: 'application/vnd.ms-excel',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      ppt: 'application/vnd.ms-powerpoint',
      pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      // 文本
      txt: 'text/plain',
      csv: 'text/csv',
      json: 'application/json',
      xml: 'application/xml',
      // 压缩文件
      zip: 'application/zip',
      rar: 'application/x-rar-compressed',
      '7z': 'application/x-7z-compressed',
      // 音频
      mp3: 'audio/mpeg',
      wav: 'audio/wav',
      ogg: 'audio/ogg',
      // 视频
      mp4: 'video/mp4',
      avi: 'video/x-msvideo',
      mov: 'video/quicktime',
      webm: 'video/webm',
    }
    
    return mimeTypes[ext || ''] || 'application/octet-stream'
  }
}
