// 全局类型使用示例
// 这个文件展示了如何使用全局类型声明，无需导入即可直接使用

// 1. 使用 ApiResponse 类型
function handleApiResponse(response: ApiResponse<{ name: string; age: number }>) {
  if (response.success && response.data) {
    console.log(`用户: ${response.data.name}, 年龄: ${response.data.age}`)
  } else {
    console.error('API 错误:', response.error)
  }
}

// 2. 使用 PaginatedResponse 类型
function handleUserList(response: PaginatedResponse<{ id: string; name: string }>) {
  console.log(`总共 ${response.pagination.total} 个用户`)
  response.data.forEach(user => {
    console.log(`用户 ID: ${user.id}, 姓名: ${user.name}`)
  })
}

// 3. 使用工具类型
interface User {
  id: string
  name: string
  email: string
  age: number
  createdAt: Date
}

// 创建用户时，某些字段是可选的
type CreateUserRequest = Optional<User, 'id' | 'createdAt'>

// 更新用户时，除了 id 外其他字段都是可选的
type UpdateUserRequest = RequiredFields<Partial<User>, 'id'>

function createUser(userData: CreateUserRequest): User {
  return {
    id: crypto.randomUUID(),
    createdAt: new Date(),
    ...userData
  }
}

function updateUser(userData: UpdateUserRequest): User {
  // 更新用户逻辑
  return {
    id: userData.id,
    name: userData.name || '默认名称',
    email: userData.email || '<EMAIL>',
    age: userData.age || 0,
    createdAt: new Date()
  }
}

// 4. 使用 WithId 类型
type Product = WithId<{
  name: string
  price: number
  category: string
}>

function processProduct(product: Product) {
  console.log(`产品 ${product.id}: ${product.name} - ¥${product.price}`)
}

// 5. 使用搜索和过滤类型
function searchUsers(params: SearchParams<User>): PaginatedResponse<User> {
  // 模拟搜索逻辑
  console.log('搜索参数:', params)
  
  return {
    data: [
      { id: '1', name: '张三', email: '<EMAIL>', age: 25, createdAt: new Date() },
      { id: '2', name: '李四', email: '<EMAIL>', age: 30, createdAt: new Date() }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 2,
      totalPages: 1
    }
  }
}

// 6. 使用错误类型
function handleError(error: AppError) {
  console.error(`错误 [${error.code}]: ${error.message}`)
  if (error.details) {
    console.error('错误详情:', error.details)
  }
}

// 7. 使用通知类型
function showNotification(notification: Notification) {
  const icon = {
    info: 'ℹ️',
    success: '✅',
    warning: '⚠️',
    error: '❌'
  }[notification.type]
  
  console.log(`${icon} ${notification.title}: ${notification.message}`)
}

// 8. 使用文件上传类型
function handleFileUpload(file: FileUpload) {
  console.log(`文件上传成功: ${file.originalName} (${file.size} bytes)`)
  console.log(`访问地址: ${file.url}`)
}

// 9. 使用审计日志类型
function logUserAction(log: AuditLog) {
  console.log(`用户 ${log.userId} 对 ${log.resource}(${log.resourceId}) 执行了 ${log.action} 操作`)
  if (log.changes) {
    Object.entries(log.changes).forEach(([field, change]) => {
      console.log(`  ${field}: ${change.old} -> ${change.new}`)
    })
  }
}

// 示例使用
export function demonstrateGlobalTypes() {
  // API 响应示例
  const apiResponse: ApiResponse<{ name: string; age: number }> = {
    success: true,
    data: { name: '张三', age: 25 }
  }
  handleApiResponse(apiResponse)

  // 分页响应示例
  const userListResponse: PaginatedResponse<{ id: string; name: string }> = {
    data: [
      { id: '1', name: '张三' },
      { id: '2', name: '李四' }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 2,
      totalPages: 1
    }
  }
  handleUserList(userListResponse)

  // 创建用户示例
  const newUser = createUser({
    name: '王五',
    email: '<EMAIL>',
    age: 28
  })
  console.log('新用户:', newUser)

  // 搜索用户示例
  const searchResult = searchUsers({
    query: '张',
    filters: [
      { field: 'age', operator: 'gte', value: 18 }
    ],
    sort: [
      { field: 'createdAt', order: 'desc' }
    ],
    pagination: { page: 1, limit: 10 }
  })
  console.log('搜索结果:', searchResult)

  // 错误处理示例
  const error: AppError = {
    code: 'USER_NOT_FOUND',
    message: '用户不存在',
    statusCode: 404,
    details: { userId: '123' }
  }
  handleError(error)

  // 通知示例
  const notification: Notification = {
    id: '1',
    type: 'success',
    title: '操作成功',
    message: '用户创建成功',
    read: false,
    createdAt: new Date()
  }
  showNotification(notification)
}
