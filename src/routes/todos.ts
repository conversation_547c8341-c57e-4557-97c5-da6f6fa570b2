import { drizzle } from 'drizzle-orm/d1'
import { Hono } from 'hono'
import { todos } from '@/db/schema'
import { TodoService } from '@/services/todoService'
import type { CreateTodoRequest } from '@/types/todos'

const todosRouter = new Hono<HonoContext>().basePath('/todos')

// 获取用户的所有待办事项
todosRouter.get('/', async (c) => {
  return c.json({ todos: [] })
})

// 创建新的待办事项
todosRouter.post('/', async (c) => {
  const user = c.get('user')
  console.log('add', user)
  const db = drizzle(c.env.DB)

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  const body: CreateTodoRequest = await c.req.json()

  if (!body.title) {
    return c.json({ error: 'Title is required' }, 400)
  }

  const result = db
    .insert(todos)
    .values({
      description: body.description,
      dueDate: body.dueDate ? new Date(body.dueDate) : null,
      priority: body.priority || 'medium',
      title: body.title,
      userId: user.id,
    })
    .returning()

  return c.json({ todo: result }, 201)
})

// 获取单个待办事项详情
todosRouter.get('/:id', async (c) => {
  const todoService = new TodoService(c.env.DB)
  const user = c.get('user')
  const todoId = c.req.param('id')

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  const todo = await todoService.getTodoById(todoId, user.id)

  if (!todo) {
    return c.json({ error: 'Todo not found' }, 404)
  }

  return c.json({ todo })
})

export default todosRouter
