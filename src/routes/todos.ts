import { createRoute, z } from '@hono/zod-openapi'
import { zValidator } from '@hono/zod-validator'
import { and, eq } from 'drizzle-orm'
import { Hono } from 'hono'
import { db } from '@/db'
import { todos } from '@/db/schema'
import type { CreateTodoRequest } from '@/types/todos'

const todosRouter = new Hono<HonoContext>().basePath('/todos')

const ParamsSchema = z.object({
  id: z
    .string()
    .min(3)
    .openapi({
      example: '1212121',
      param: {
        in: 'path',
        name: 'id',
      },
    }),
})
const UserSchema = z
  .object({
    age: z.number().openapi({
      example: 42,
    }),
    id: z.string().openapi({
      example: '123',
    }),
    name: z.string().openapi({
      example: 'John Doe',
    }),
  })
  .openapi('User')
const route = createRoute({
  method: 'get',
  path: '/users/{id}',
  request: {
    params: ParamsSchema,
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: UserSchema,
        },
      },
      description: 'Retrieve the user',
    },
  },
})

// 获取用户的所有待办事项
todosRouter.get('/', async (c) => {
  const user = c.get('user')
  const results = await db.select().from(todos).where(eq(todos.userId, user.id))
  return c.json(results)
})

// 创建新的待办事项
todosRouter.post('/', async (c) => {
  const user = c.get('user')
  console.log('add', user)

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401)
  }

  const body: CreateTodoRequest = await c.req.json()

  if (!body.title) {
    return c.json({ error: 'Title is required' }, 400)
  }

  const result = await db
    .insert(todos)
    .values({
      completed: false,
      description: body.description,
      dueDate: body.dueDate ? new Date(body.dueDate) : null,
      priority: body.priority || 'medium',
      title: body.title,
      userId: user.id,
    })
    .returning()

  return c.json(result[0], 201)
})

const userIdParamSchema = z.object({
  id: z.string().regex(/^\d+$/, '用户ID必须是数字').transform(Number), // 转换为数字
})

// 获取单个待办事项详情
todosRouter.get('/:id', zValidator('param', userIdParamSchema), async (c) => {
  const user = c.get('user')
  const todoId = c.req.param('id')
  console.log('todoId', todoId)
  const todo = await db.query.todos.findFirst({
    where: and(eq(todos.id, Number(todoId)), eq(todos.userId, user.id)),
  })

  if (!todo) {
    return c.json({ message: '任务不存在' }, 404)
  }

  return c.json(todo)
})

export default todosRouter
