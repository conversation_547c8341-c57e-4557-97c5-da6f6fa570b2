import { zValidator } from '@hono/zod-validator'
import { Hono } from 'hono'
import { z } from 'zod'
import { R2StorageService } from '@/services/r2StorageService'

const filesRouter = new Hono<HonoContext>().basePath('/files')

// 文件上传验证 schema
const uploadSchema = z.object({
  category: z.string().optional().default('general'),
  metadata: z.record(z.string(), z.any()).optional(),
})

// 文件上传路由
filesRouter.post('/upload', zValidator('form', uploadSchema), async (c) => {
  const user = c.get('user')
  if (!user) {
    return c.json({ error: '未授权访问' }, 401)
  }

  try {
    // 获取上传的文件
    const formData = await c.req.formData()
    const file = formData.get('file') as File
    const { category, metadata } = c.req.valid('form')

    if (!file) {
      return c.json({ error: '未找到上传的文件' }, 400)
    }

    // 验证文件大小（限制为 10MB）
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return c.json({ error: '文件大小不能超过 10MB' }, 400)
    }

    // 验证文件类型
    const allowedTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'application/pdf',
      'text/plain',
      'application/json',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ]

    if (!allowedTypes.includes(file.type)) {
      return c.json({ error: '不支持的文件类型' }, 400)
    }

    // 初始化 R2 存储服务
    const r2Service = new R2StorageService(c.env.STORAGE_BUCKET)

    // 生成文件键名
    const fileKey = R2StorageService.generateUserFileKey(
      user.id,
      file.name,
      category,
    )

    // 准备上传选项
    const uploadOptions = {
      cacheControl: 'public, max-age=31536000', // 1年缓存
      contentType: file.type,
      metadata: {
        originalName: file.name,
        uploadedAt: new Date().toISOString(),
        userId: user.id,
        ...metadata,
      },
    }

    // 上传文件
    const fileInfo = await r2Service.uploadFile(fileKey, file, uploadOptions)

    // 生成访问 URL
    const accessUrl = r2Service.generateSignedUrl(fileKey, 3600 * 24 * 7) // 7天有效期

    return c.json(
      {
        data: {
          accessUrl,
          contentType: fileInfo.contentType,
          etag: fileInfo.etag,
          key: fileInfo.key,
          metadata: fileInfo.metadata,
          size: fileInfo.size,
          uploaded: fileInfo.uploaded,
        },
        success: true,
      },
      201,
    )
  } catch (error) {
    console.error('文件上传失败:', error)
    return c.json({ error: '文件上传失败' }, 500)
  }
})

// 文件下载/访问路由
filesRouter.get('/:key{.+}', async (c) => {
  try {
    const key = c.req.param('key')
    const expires = c.req.query('expires')

    // 验证签名（简单的时间戳验证）
    if (expires) {
      const expiresTime = parseInt(expires)
      const currentTime = Math.floor(Date.now() / 1000)

      if (currentTime > expiresTime) {
        return c.json({ error: '链接已过期' }, 403)
      }
    }

    // 初始化 R2 存储服务
    const r2Service = new R2StorageService(c.env.STORAGE_BUCKET)

    // 获取文件
    const file = await r2Service.getFile(key)

    if (!file) {
      return c.json({ error: '文件不存在' }, 404)
    }

    // 设置响应头
    const headers = new Headers()
    if (file.httpMetadata?.contentType) {
      headers.set('Content-Type', file.httpMetadata.contentType)
    }
    if (file.httpMetadata?.cacheControl) {
      headers.set('Cache-Control', file.httpMetadata.cacheControl)
    }
    if (file.httpMetadata?.contentDisposition) {
      headers.set('Content-Disposition', file.httpMetadata.contentDisposition)
    }

    // 返回文件流
    return new Response(file.body, {
      headers,
      status: 200,
    })
  } catch (error) {
    console.error('文件访问失败:', error)
    return c.json({ error: '文件访问失败' }, 500)
  }
})

// 获取用户文件列表
filesRouter.get('/', async (c) => {
  const user = c.get('user')
  if (!user) {
    return c.json({ error: '未授权访问' }, 401)
  }

  try {
    const category = c.req.query('category')
    const limit = parseInt(c.req.query('limit') || '20')
    const cursor = c.req.query('cursor')

    // 初始化 R2 存储服务
    const r2Service = new R2StorageService(c.env.STORAGE_BUCKET)

    // 构建前缀
    let prefix = `users/${user.id}`
    if (category) {
      prefix += `/${category}`
    }

    // 获取文件列表
    const result = await r2Service.listFiles(prefix, limit, cursor)

    // 格式化响应
    const files = result.objects.map((obj) => ({
      contentType: obj.httpMetadata?.contentType,
      etag: obj.etag,
      key: obj.key,
      metadata: obj.customMetadata,
      size: obj.size,
      uploaded: obj.uploaded,
    }))

    return c.json({
      data: {
        cursor: result.truncated ? result.cursor : undefined,
        files,
        truncated: result.truncated,
      },
      success: true,
    })
  } catch (error) {
    console.error('获取文件列表失败:', error)
    return c.json({ error: '获取文件列表失败' }, 500)
  }
})

// 删除文件
filesRouter.delete('/:key{.+}', async (c) => {
  const user = c.get('user')
  if (!user) {
    return c.json({ error: '未授权访问' }, 401)
  }

  try {
    const key = c.req.param('key')

    // 验证文件是否属于当前用户
    if (!key.startsWith(`users/${user.id}/`)) {
      return c.json({ error: '无权限删除此文件' }, 403)
    }

    // 初始化 R2 存储服务
    const r2Service = new R2StorageService(c.env.STORAGE_BUCKET)

    // 检查文件是否存在
    const fileInfo = await r2Service.getFileInfo(key)
    if (!fileInfo) {
      return c.json({ error: '文件不存在' }, 404)
    }

    // 删除文件
    await r2Service.deleteFile(key)

    return c.json({
      message: '文件删除成功',
      success: true,
    })
  } catch (error) {
    console.error('文件删除失败:', error)
    return c.json({ error: '文件删除失败' }, 500)
  }
})

export default filesRouter
