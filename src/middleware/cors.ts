import type { MiddlewareHandler } from 'hono'
import { cors } from 'hono/cors'

type Middleware = MiddlewareHandler<
  {
    Bindings: CloudflareBindings
    Variables: HonoVariables
  },
  '*',
  Record<string, unknown>
>

/**
 * CORS 中间件
 */
export const corsMiddlewareHandler: Middleware = async (c, next) => {
  const handler = cors({
    allowHeaders: ['Content-Type', 'Authorization'],
    allowMethods: ['POST', 'GET', 'OPTIONS'],
    credentials: true,
    exposeHeaders: ['Content-Length'],
    maxAge: 600,
    origin: [c.env.BETTER_AUTH_URL, 'http://localhost:3000'],
  })
  return handler(c, next)
}
