import type { Context, Next } from 'hono'
import { whiteRoutes } from '@/constants'
import { auth } from '@/lib/better-auth'

/**
 * 路由鉴权中间件
 */
export const authMiddlewareHandler = async (
  c: Context<{
    Bindings: CloudflareBindings
    Variables: HonoVariables
  }>,
  next: Next,
) => {
  // 排除白名单路径的 session 校验
  if (whiteRoutes.includes(c.req.path) || c.req.path.startsWith('/api/auth')) {
    return next()
  }

  const session = await auth(c.env).api.getSession({
    headers: c.req.raw.headers,
  })
  if (!session) {
    c.status(401)
    return c.json({
      code: 401,
      message: '未登录',
    })
  }

  c.set('user', session.user)
  c.set('session', session.session)
  return next()
}
