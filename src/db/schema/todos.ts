import { sql } from 'drizzle-orm'
import {
  integer,
  sqliteTable,
  text,
  uniqueIndex,
} from 'drizzle-orm/sqlite-core'
import { user } from './auth'

// 任务分类表
export const categories = sqliteTable(
  'categories',
  {
    color: text('color').default('#3B82F6'), // 默认蓝色
    createdAt: integer('created_at', { mode: 'timestamp' })
      .$defaultFn(() => new Date())
      .default(sql`(unixepoch() * 1000)`)
      .notNull(),
    icon: text('icon'), // 图标名称或emoji
    id: text('id')
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    name: text('name').notNull(),
    updatedAt: integer('updated_at', { mode: 'timestamp' })
      .$defaultFn(() => new Date())
      .default(sql`(unixepoch() * 1000)`)
      .notNull(),
    userId: text('user_id')
      .notNull()
      .references(() => user.id, { onDelete: 'cascade' }),
  },
  (table) => [uniqueIndex('categories_user_id_idx').on(table.userId)],
)

// 任务表
export const todos = sqliteTable(
  'todos',
  {
    categoryId: text('category_id').references(() => categories.id, {
      onDelete: 'set null',
    }),
    completed: integer('completed', { mode: 'boolean' })
      .default(false)
      .notNull(),
    completedAt: integer('completed_at', { mode: 'timestamp' }),

    // 时间戳
    createdAt: integer('created_at', { mode: 'timestamp' })
      .$defaultFn(() => new Date())
      .notNull(),
    description: text('description'),
    dueDate: integer('due_date', { mode: 'timestamp' }),
    id: text('id')
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    priority: text('priority', { enum: ['low', 'medium', 'high'] })
      .default('medium')
      .notNull(),

    // 排序字段
    sortOrder: integer('sort_order').default(0).notNull(),
    title: text('title').notNull(),
    updatedAt: integer('updated_at', { mode: 'timestamp' })
      .$defaultFn(() => new Date())
      .notNull(),

    // 关联字段
    userId: text('user_id')
      .notNull()
      .references(() => user.id, { onDelete: 'cascade' }),
  },
  (table) => [
    uniqueIndex('todos_category_id_idx').on(table.categoryId),
    uniqueIndex('todos_completed_idx').on(table.completed),
    uniqueIndex('todos_due_date_idx').on(table.dueDate),
    uniqueIndex('todos_priority_idx').on(table.priority),
    uniqueIndex('todos_user_id_idx').on(table.userId),
  ],
)

// 任务标签表
export const tags = sqliteTable(
  'tags',
  {
    color: text('color').default('#6B7280'), // 默认灰色
    createdAt: integer('created_at', { mode: 'timestamp' })
      .$defaultFn(() => new Date())
      .notNull(),
    id: text('id')
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    name: text('name').notNull(),
    userId: text('user_id')
      .notNull()
      .references(() => user.id, { onDelete: 'cascade' }),
  },
  (table) => [
    uniqueIndex('tags_user_id_idx').on(table.userId),
    uniqueIndex('tags_user_name_idx').on(table.userId, table.name),
  ],
)

// 任务标签关联表（多对多关系）
export const todoTags = sqliteTable(
  'todo_tags',
  {
    createdAt: integer('created_at', { mode: 'timestamp' })
      .$defaultFn(() => new Date())
      .notNull(),
    id: text('id')
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    tagId: text('tag_id')
      .notNull()
      .references(() => tags.id, { onDelete: 'cascade' }),
    todoId: text('todo_id')
      .notNull()
      .references(() => todos.id, { onDelete: 'cascade' }),
  },
  (table) => [
    uniqueIndex('todo_tags_tag_id_idx').on(table.tagId),
    uniqueIndex('todo_tags_todo_id_idx').on(table.todoId),
    uniqueIndex('todo_tags_todo_tag_idx').on(table.todoId, table.tagId),
  ],
)

// 任务附件表
export const todoAttachments = sqliteTable(
  'todo_attachments',
  {
    createdAt: integer('created_at', { mode: 'timestamp' })
      .$defaultFn(() => new Date())
      .notNull(),
    fileName: text('file_name').notNull(),
    fileSize: integer('file_size'), // 文件大小（字节）
    fileUrl: text('file_url').notNull(),
    id: text('id')
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    mimeType: text('mime_type'),
    todoId: text('todo_id')
      .notNull()
      .references(() => todos.id, { onDelete: 'cascade' }),
  },
  (table) => [uniqueIndex('todo_attachments_todo_id_idx').on(table.todoId)],
)

// 任务评论表
export const todoComments = sqliteTable(
  'todo_comments',
  {
    content: text('content').notNull(),
    createdAt: integer('created_at', { mode: 'timestamp' })
      .$defaultFn(() => new Date())
      .notNull(),
    id: text('id')
      .primaryKey()
      .$defaultFn(() => crypto.randomUUID()),
    todoId: text('todo_id')
      .notNull()
      .references(() => todos.id, { onDelete: 'cascade' }),
    updatedAt: integer('updated_at', { mode: 'timestamp' })
      .$defaultFn(() => new Date())
      .notNull(),
    userId: text('user_id')
      .notNull()
      .references(() => user.id, { onDelete: 'cascade' }),
  },
  (table) => [
    uniqueIndex('todo_comments_todo_id_idx').on(table.todoId),
    uniqueIndex('todo_comments_user_id_idx').on(table.userId),
  ],
)
