import { sql } from 'drizzle-orm'
import { integer, sqliteTable, text } from 'drizzle-orm/sqlite-core'
import { user } from './auth'

export const todos = sqliteTable('todos', {
  /** 是否完成 */
  completed: integer('completed', { mode: 'boolean' }).notNull().default(false),
  /** 完成时间 */
  completedAt: integer('completed_at', { mode: 'timestamp_ms' }),
  /** 创建时间 */
  createdAt: integer('created_at', { mode: 'timestamp_ms' })
    .notNull()
    .default(sql`(unixepoch() * 1000)`),
  /** 描述 */
  description: text('description'),
  /** 截止时间 */
  dueDate: integer('due_date', { mode: 'timestamp_ms' }),
  id: integer('id').primaryKey({ autoIncrement: true }),
  /** 优先级 */
  priority: text('priority', { enum: ['low', 'medium', 'high'] })
    .notNull()
    .default('medium'),
  title: text('title').notNull(),
  /** 更新时间 */
  updatedAt: integer('updated_at', { mode: 'timestamp_ms' })
    .notNull()
    .default(sql`(unixepoch() * 1000)`)
    .$onUpdate(() => sql`(unixepoch() * 1000)`),
  /** 用户ID */
  userId: text('user_id')
    .notNull()
    .references(() => user.id, { onDelete: 'cascade' }),
})
