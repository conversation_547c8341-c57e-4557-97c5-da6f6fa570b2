// utils/validationFormatter.ts
import type { z } from 'zod'

export interface FormattedValidationError {
  field: string
  messages: string[]
  type?: string
}

export function formatValidationError(zodError: z.ZodError): {
  message: string
  errors: FormattedValidationError[]
} {
  const fieldErrors: Record<string, FormattedValidationError> = {}
  zodError.issues.forEach((issue) => {
    const field = issue.path.join('.') || 'root'
    const message = getFriendlyErrorMessage(issue)

    if (!fieldErrors[field]) {
      fieldErrors[field] = {
        field,
        messages: [],
        type: issue.code,
      }
    }

    fieldErrors[field].messages.push(message)
  })

  return {
    errors: Object.values(fieldErrors),
    message: '数据校验失败',
  }
}

function getFriendlyErrorMessage(issue: z.core.$ZodIssue): string {
  const { code, message, path } = issue
  const field = path[path.length - 1] || '字段'

  switch (code) {
    case 'invalid_type':
      return `"${field}" 类型不正确`
    case 'too_small':
      return `"${field}" ${issue.type === 'string' ? '长度' : '值'}过小`
    case 'too_big':
      return `"${field}" ${issue.type === 'string' ? '长度' : '值'}过大`
    case 'invalid_string':
      if (issue.validation === 'email') {
        return '请输入有效的邮箱地址'
      }
      if (issue.validation === 'url') {
        return '请输入有效的网址'
      }
      return message
    default:
      return message
  }
}
