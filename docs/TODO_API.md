# Todo List API 文档

## 概述

这是一个功能完整的 Todo List 后端 API，基于 Hono + Cloudflare Workers + Drizzle ORM + Better Auth 构建。

## 数据库设计

### 核心表结构

1. **todos** - 主要的待办事项表
   - 支持优先级（low/medium/high）
   - 支持截止日期
   - 支持分类
   - 支持父子任务关系（子任务）
   - 支持排序

2. **categories** - 任务分类表
   - 自定义颜色和图标
   - 用户隔离

3. **tags** - 标签表
   - 多对多关系（一个任务可以有多个标签）
   - 自定义颜色

4. **todo_attachments** - 附件表
   - 支持文件上传

5. **todo_comments** - 评论表
   - 支持任务评论

## API 端点

### 认证

所有 API 都需要用户认证，除了 `/api/auth/*` 路径。

### Todo 管理

#### 获取待办事项列表
```
GET /api/todos
```

查询参数：
- `completed`: boolean - 过滤已完成/未完成任务
- `categoryId`: string - 按分类过滤
- `priority`: 'low'|'medium'|'high' - 按优先级过滤
- `parentId`: string|'null' - 获取子任务或顶级任务

响应：
```json
{
  "todos": [
    {
      "id": "uuid",
      "title": "任务标题",
      "description": "任务描述",
      "completed": false,
      "priority": "medium",
      "dueDate": "2024-01-01T00:00:00Z",
      "completedAt": null,
      "parentId": null,
      "sortOrder": 0,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "category": {
        "id": "uuid",
        "name": "工作",
        "color": "#3B82F6",
        "icon": "💼"
      }
    }
  ]
}
```

#### 创建待办事项
```
POST /api/todos
```

请求体：
```json
{
  "title": "任务标题",
  "description": "任务描述",
  "priority": "medium",
  "dueDate": "2024-01-01T00:00:00Z",
  "categoryId": "uuid",
  "parentId": "uuid"
}
```

#### 获取单个待办事项
```
GET /api/todos/:id
```

#### 更新待办事项
```
PUT /api/todos/:id
```

请求体：
```json
{
  "title": "新标题",
  "completed": true,
  "priority": "high"
}
```

#### 删除待办事项
```
DELETE /api/todos/:id
```

#### 获取子任务
```
GET /api/todos/:id/subtasks
```

### 分类管理

#### 获取分类列表
```
GET /api/todos/categories
```

#### 创建分类
```
POST /api/todos/categories
```

请求体：
```json
{
  "name": "分类名称",
  "color": "#3B82F6",
  "icon": "💼"
}
```

### 统计信息

#### 获取统计数据
```
GET /api/todos/stats
```

响应：
```json
{
  "stats": {
    "total": 10,
    "completed": 5,
    "pending": 4,
    "overdue": 1
  }
}
```

## 功能特性

### 已实现
- ✅ 用户认证和授权
- ✅ CRUD 操作（创建、读取、更新、删除）
- ✅ 任务分类
- ✅ 优先级管理
- ✅ 截止日期
- ✅ 父子任务关系（子任务）
- ✅ 任务排序
- ✅ 过滤和查询
- ✅ 统计信息
- ✅ 数据库索引优化

### 可扩展功能
- 🔄 标签系统（已设计表结构）
- 🔄 文件附件（已设计表结构）
- 🔄 任务评论（已设计表结构）
- 🔄 任务提醒
- 🔄 任务模板
- 🔄 团队协作
- 🔄 任务时间跟踪

## 使用示例

### 创建一个工作任务
```bash
curl -X POST http://localhost:5173/api/todos \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "title": "完成项目文档",
    "description": "编写 API 文档和用户手册",
    "priority": "high",
    "dueDate": "2024-01-15T18:00:00Z",
    "categoryId": "work-category-id"
  }'
```

### 获取未完成的高优先级任务
```bash
curl "http://localhost:5173/api/todos?completed=false&priority=high" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 标记任务为完成
```bash
curl -X PUT http://localhost:5173/api/todos/TASK_ID \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"completed": true}'
```

## 部署

1. 生成数据库迁移：
```bash
npm run db:generate
```

2. 应用迁移：
```bash
npm run db:migrate
```

3. 启动开发服务器：
```bash
npm run dev
```

4. 部署到 Cloudflare Workers：
```bash
npm run deploy
```
