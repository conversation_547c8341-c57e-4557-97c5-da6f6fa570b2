# Cloudflare R2 存储使用示例

## API 端点

### 1. 文件上传

**POST** `/api/files/upload`

```bash
# 使用 curl 上传文件
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@/path/to/your/file.jpg" \
  -F "category=avatar" \
  -F "metadata[description]=用户头像" \
  http://localhost:5173/api/files/upload
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "key": "users/user123/avatar/1703123456789_abc123_profile.jpg",
    "size": 245760,
    "etag": "d41d8cd98f00b204e9800998ecf8427e",
    "uploaded": "2023-12-21T10:30:45.123Z",
    "contentType": "image/jpeg",
    "accessUrl": "/api/files/users%2Fuser123%2Favatar%2F1703123456789_abc123_profile.jpg?expires=1703209845",
    "metadata": {
      "userId": "user123",
      "originalName": "profile.jpg",
      "uploadedAt": "2023-12-21T10:30:45.123Z",
      "description": "用户头像"
    }
  }
}
```

### 2. 获取文件列表

**GET** `/api/files`

```bash
# 获取所有文件
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:5173/api/files"

# 按分类过滤
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:5173/api/files?category=avatar&limit=10"

# 分页获取
curl -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:5173/api/files?cursor=CURSOR_STRING&limit=20"
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "key": "users/user123/avatar/1703123456789_abc123_profile.jpg",
        "size": 245760,
        "etag": "d41d8cd98f00b204e9800998ecf8427e",
        "uploaded": "2023-12-21T10:30:45.123Z",
        "contentType": "image/jpeg",
        "metadata": {
          "userId": "user123",
          "originalName": "profile.jpg",
          "description": "用户头像"
        }
      }
    ],
    "truncated": false,
    "cursor": null
  }
}
```

### 3. 访问文件

**GET** `/api/files/{key}`

```bash
# 直接访问文件
curl "http://localhost:5173/api/files/users%2Fuser123%2Favatar%2F1703123456789_abc123_profile.jpg"

# 使用签名 URL 访问
curl "http://localhost:5173/api/files/users%2Fuser123%2Favatar%2F1703123456789_abc123_profile.jpg?expires=1703209845"
```

### 4. 删除文件

**DELETE** `/api/files/{key}`

```bash
curl -X DELETE \
  -H "Authorization: Bearer YOUR_TOKEN" \
  "http://localhost:5173/api/files/users%2Fuser123%2Favatar%2F1703123456789_abc123_profile.jpg"
```

**响应示例：**
```json
{
  "success": true,
  "message": "文件删除成功"
}
```

## JavaScript/TypeScript 客户端示例

### 文件上传

```typescript
async function uploadFile(file: File, category: string = 'general') {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('category', category)
  formData.append('metadata[description]', '文件描述')

  const response = await fetch('/api/files/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
    body: formData,
  })

  if (!response.ok) {
    throw new Error('上传失败')
  }

  return await response.json()
}

// 使用示例
const fileInput = document.getElementById('file') as HTMLInputElement
const file = fileInput.files?.[0]

if (file) {
  try {
    const result = await uploadFile(file, 'avatar')
    console.log('上传成功:', result.data.accessUrl)
  } catch (error) {
    console.error('上传失败:', error)
  }
}
```

### 获取文件列表

```typescript
async function getFileList(category?: string, limit: number = 20, cursor?: string) {
  const params = new URLSearchParams()
  if (category) params.append('category', category)
  params.append('limit', limit.toString())
  if (cursor) params.append('cursor', cursor)

  const response = await fetch(`/api/files?${params}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  })

  if (!response.ok) {
    throw new Error('获取文件列表失败')
  }

  return await response.json()
}

// 使用示例
try {
  const result = await getFileList('avatar', 10)
  console.log('文件列表:', result.data.files)
} catch (error) {
  console.error('获取失败:', error)
}
```

### 删除文件

```typescript
async function deleteFile(fileKey: string) {
  const response = await fetch(`/api/files/${encodeURIComponent(fileKey)}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  })

  if (!response.ok) {
    throw new Error('删除失败')
  }

  return await response.json()
}

// 使用示例
try {
  await deleteFile('users/user123/avatar/1703123456789_abc123_profile.jpg')
  console.log('删除成功')
} catch (error) {
  console.error('删除失败:', error)
}
```

## React 组件示例

### 文件上传组件

```tsx
import React, { useState } from 'react'

interface FileUploadProps {
  category?: string
  onUploadSuccess?: (fileInfo: any) => void
  onUploadError?: (error: string) => void
}

export const FileUpload: React.FC<FileUploadProps> = ({
  category = 'general',
  onUploadSuccess,
  onUploadError,
}) => {
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setUploading(true)
    setProgress(0)

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('category', category)

      const response = await fetch('/api/files/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: formData,
      })

      if (!response.ok) {
        throw new Error('上传失败')
      }

      const result = await response.json()
      onUploadSuccess?.(result.data)
    } catch (error) {
      onUploadError?.(error instanceof Error ? error.message : '上传失败')
    } finally {
      setUploading(false)
      setProgress(0)
    }
  }

  return (
    <div className="file-upload">
      <input
        type="file"
        onChange={handleFileUpload}
        disabled={uploading}
        accept="image/*,.pdf,.doc,.docx"
      />
      {uploading && (
        <div className="upload-progress">
          <div>上传中... {progress}%</div>
        </div>
      )}
    </div>
  )
}
```

## 错误处理

### 常见错误码

- `400` - 请求参数错误（文件缺失、类型不支持等）
- `401` - 未授权访问
- `403` - 权限不足或链接已过期
- `404` - 文件不存在
- `413` - 文件大小超出限制
- `500` - 服务器内部错误

### 错误响应格式

```json
{
  "error": "文件大小不能超过 10MB",
  "code": "FILE_TOO_LARGE"
}
```
