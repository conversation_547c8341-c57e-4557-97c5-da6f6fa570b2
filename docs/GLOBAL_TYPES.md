# 全局类型声明使用指南

本项目使用全局类型声明来提供常用的类型定义，这些类型可以在整个项目中直接使用，无需导入。

## 文件结构

```
项目根目录/
├── global.d.ts                    # 全局类型声明文件
├── src/
│   ├── types/
│   │   ├── global.ts              # 项目特定的类型（需要导入）
│   │   ├── todos.ts               # Todo 相关类型
│   │   └── index.d.ts             # 额外的全局类型声明
│   └── examples/
│       └── global-types-usage.ts  # 全局类型使用示例
└── tsconfig.json                  # TypeScript 配置
```

## 全局类型列表

### 1. API 响应类型

```typescript
// 通用 API 响应
type ApiResponse<T = unknown> = {
  success: boolean
  data?: T
  message?: string
  error?: string
}

// 分页参数
type PaginationParams = {
  page?: number
  limit?: number
  offset?: number
}

// 分页响应
type PaginatedResponse<T> = {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}
```

### 2. 工具类型

```typescript
// 使某些字段可选
type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

// 使某些字段必需
type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// 添加 ID 字段
type WithId<T = Record<string, unknown>> = T & { id: string }

// 时间戳字段
type Timestamp = {
  createdAt: Date
  updatedAt: Date
}
```

### 3. 搜索和过滤类型

```typescript
// 排序
type SortOrder = 'asc' | 'desc'
type SortBy<T> = {
  field: keyof T
  order: SortOrder
}

// 过滤器
type FilterOperator = 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'like' | 'regex'
type Filter<T> = {
  field: keyof T
  operator: FilterOperator
  value: unknown
}

// 搜索参数
type SearchParams<T> = {
  query?: string
  filters?: Filter<T>[]
  sort?: SortBy<T>[]
  pagination?: PaginationParams
}
```

### 4. 业务类型

```typescript
// 环境类型
type Environment = 'development' | 'production' | 'test'

// 状态类型
type Status = 'active' | 'inactive' | 'pending' | 'deleted'

// HTTP 方法
type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE' | 'HEAD' | 'OPTIONS'

// 错误类型
type AppError = {
  code: string
  message: string
  details?: Record<string, unknown>
  statusCode?: number
}

// 通知类型
type NotificationType = 'info' | 'success' | 'warning' | 'error'
type Notification = {
  id: string
  type: NotificationType
  title: string
  message: string
  read: boolean
  createdAt: Date
}
```

## 使用方法

### 1. 直接使用（无需导入）

```typescript
// 在任何 .ts 或 .tsx 文件中直接使用
function handleApiResponse(response: ApiResponse<User>) {
  if (response.success && response.data) {
    console.log('用户数据:', response.data)
  }
}

// 使用工具类型
interface User {
  id: string
  name: string
  email: string
  age: number
}

// 创建用户时某些字段可选
type CreateUserRequest = Optional<User, 'id'>

// 带 ID 的产品类型
type Product = WithId<{
  name: string
  price: number
}>
```

### 2. 在函数参数中使用

```typescript
function searchUsers(params: SearchParams<User>): PaginatedResponse<User> {
  // 实现搜索逻辑
  return {
    data: [],
    pagination: {
      page: params.pagination?.page || 1,
      limit: params.pagination?.limit || 10,
      total: 0,
      totalPages: 0
    }
  }
}
```

### 3. 在组件中使用

```typescript
// React 组件中使用
interface Props {
  users: PaginatedResponse<User>
  onError: (error: AppError) => void
}

function UserList({ users, onError }: Props) {
  // 组件实现
}
```

## 添加新的全局类型

### 1. 在 global.d.ts 中添加

```typescript
declare global {
  // 添加新的全局类型
  type MyNewType = {
    // 类型定义
  }
}
```

### 2. 重启 TypeScript 服务

在 VS Code 中：
1. 按 `Cmd/Ctrl + Shift + P`
2. 输入 "TypeScript: Restart TS Server"
3. 选择并执行

## 最佳实践

### 1. 命名规范
- 使用 PascalCase 命名类型
- 使用描述性的名称
- 避免与现有类型冲突

### 2. 类型组织
- 相关的类型放在一起
- 添加注释说明类型用途
- 保持类型定义简洁

### 3. 泛型使用
- 合理使用泛型提高类型复用性
- 为泛型参数提供默认值
- 使用约束限制泛型参数

### 4. 避免过度使用
- 只将真正通用的类型放在全局声明中
- 特定业务的类型应该放在对应的模块中
- 保持全局类型文件的整洁

## 示例代码

查看 `src/examples/global-types-usage.ts` 文件获取完整的使用示例。

## 注意事项

1. **文件必须有导出语句**：全局类型声明文件必须包含至少一个 `export` 语句才能被 TypeScript 识别为模块。

2. **重启 TS 服务**：添加新的全局类型后，需要重启 TypeScript 服务才能生效。

3. **避免命名冲突**：确保全局类型名称不与其他库或模块的类型冲突。

4. **类型检查**：全局类型同样受到 TypeScript 严格模式的检查。

5. **IDE 支持**：现代 IDE（如 VS Code）会自动识别全局类型并提供智能提示。
